import Image from "next/image";
import Link from "next/link";
import React, { useState, useRef, useEffect } from "react";
import { toast } from "react-toastify";
import { createOffice } from "@/services/api";
import { useRouter } from "next/navigation";

// List of Moroccan cities for autocomplete
const MOROCCAN_CITIES = [
  "Agadir",
  "Al Hoceima",
  "Asilah",
  "Azrou",
  "Beni Mellal",
  "Berkane",
  "Berrechid",
  "Boujdour",
  "Casablanca",
  "Chefchaouen",
  "Dakhla",
  "El Jadida",
  "Errachidia",
  "Essaouira",
  "Fès",
  "Figuig",
  "Guelmim",
  "Ifrane",
  "Kenitra",
  "Khemisset",
  "Khenifra",
  "Khouribga",
  "Laayoune",
  "Larache",
  "Marrakech",
  "Martil",
  "Meknès",
  "Mohammedia",
  "Nador",
  "Ouarzazate",
  "<PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON><PERSON> Ifni",
  "<PERSON><PERSON>",
  "Sid<PERSON> Slimane",
  "<PERSON>kh<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "Tan-Tan",
  "<PERSON>urirt",
  "<PERSON>rou<PERSON>",
  "<PERSON><PERSON>",
  "Témara",
  "Tétouan",
  "Tiznit",
];
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import GPS from "../SvgIcons/GPS";
import Map from "../SvgIcons/Map";
import Loader from "../ui/Loader";
import GradientButton from "../ui/GradientButton";

interface FormData {
  email: string;
  password: string;
  officeName: string;
  registrationNumber: string;
  licenseNumber: string;
  streetAddress: string;
  city: string;
  cityId: string;
  cityName: string;
  province: string;
  phoneNumber: string;
  whatsappNumber: string;
  otherNumber?: string;
  isWhatsAppSame?: boolean;
  logo?: string | { id: string; file?: any };
}

interface Step3FormProps {
  formData: FormData;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleNext: (formData: FormData) => void;
  handleStepChange: (step: number) => void;
}

const MAPBOX_TOKEN =
  "pk.eyJ1IjoiZXhjaGFuZ28yNCIsImEiOiJjbWJobzNtbXkwYzd2MmtzZ3M0Nmlhem1wIn0.WWU3U5Ur4wsdKokNEk1DZQ";
mapboxgl.accessToken = MAPBOX_TOKEN;

const Step3Form: React.FC<Step3FormProps> = ({
  formData,
  handleInputChange,
  handleNext,
  handleStepChange,
}) => {
  const [streetAddress, setStreetAddress] = useState(
    formData.streetAddress || ""
  );
  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);
  const [showCitySuggestions, setShowCitySuggestions] = useState(false);
  const [addressSuggestions, setAddressSuggestions] = useState<string[]>([]);
  const [showAddressSuggestions, setShowAddressSuggestions] = useState(false);
  // Prevent suggestions from reopening after selection for a short time
  const suppressSuggestionsRef = useRef(false);
  const suppressTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const [coordinates, setCoordinates] = useState<[number, number] | null>(null);
  const [map, setMap] = useState<mapboxgl.Map | null>(null);
  const [marker, setMarker] = useState<mapboxgl.Marker | null>(null);
  const mapContainer = useRef<HTMLDivElement>(null);

  const router = useRouter();

  // Check if user is authenticated
  useEffect(() => {
    const token =
      localStorage.getItem("authToken") || sessionStorage.getItem("authToken");
    if (!token) {
      toast.error("Authentication required. Please complete step 1 first.");
      handleStepChange(1);
    }

    // Check if we have logo and office information
    const hasLogo =
      typeof formData.logo === "string"
        ? !!formData.logo
        : !!(formData.logo && (formData.logo.id || formData.logo.file));

    if (!hasLogo || !formData.officeName) {
      console.error("Missing required data:", {
        logo: formData.logo,
        officeName: formData.officeName,
      });
      toast.error("Please complete office information in step 2 first.");
      handleStepChange(2);
      return;
    }

    // Debug form data
    console.log("Step3Form - formData received:", formData);
    console.log("Step3Form - Phone numbers:", {
      primaryPhoneNumber: formData.phoneNumber,
      whatsappNumber: formData.whatsappNumber,
      otherNumber: formData.otherNumber,
    });
  }, [formData.logo, formData.officeName, handleStepChange, formData]);

  // Keep only one map initialization useEffect
  useEffect(() => {
    if (!mapContainer.current) return;

    const initialMap = new mapboxgl.Map({
      container: mapContainer.current,
      style: "mapbox://styles/mapbox/streets-v12",
      center: [-7.0926, 31.7917], // Updated to center on Morocco (e.g., Marrakech coordinates)
      zoom: 9,
      attributionControl: false,
      projection: "mercator", // From your previous request for 2D map
    });

    // Add navigation control
    initialMap.addControl(new mapboxgl.NavigationControl(), "top-right");

    // Add marker in center of map with standard style
    const initialMarker = new mapboxgl.Marker({
      color: "#20523C",
      draggable: false,
    }).setLngLat([-7.0926, 31.7917]);
    initialMarker.addTo(initialMap);

    // Update marker position and coordinates when map moves
    initialMap.on("move", () => {
      const center = initialMap.getCenter();
      initialMarker.setLngLat([center.lng, center.lat]);
      setCoordinates([center.lng, center.lat]);
    });

    // Update address when map stops moving
    initialMap.on("moveend", () => {
      const center = initialMap.getCenter();
      updateAddress([center.lng, center.lat]);
    });

    // Filter out Western Sahara border when map loads
    initialMap.on("load", () => {
      try {
        initialMap.setFilter("admin-0-boundary-disputed", [
          "!=",
          ["get", "iso_3166_1"],
          "EH",
        ]);
        initialMap.setFilter("admin-0-boundary", [
          "!=",
          ["get", "iso_3166_1"],
          "EH",
        ]);
      } catch (error) {
        console.warn("Could not apply filter to admin boundaries:", error);
      }
    });

    setMap(initialMap);
    setMarker(initialMarker);

    return () => {
      initialMap.remove();
    };
  }, []);

  const fetchMapboxSuggestions = async (query: string) => {
    if (!query) return;
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          query
        )}.json?country=MA&language=fr&access_token=${MAPBOX_TOKEN}`
      );
      const data = await response.json();
      if (data.features && data.features.length > 0) {
        setAddressSuggestions(data.features.map((f: any) => f.place_name));
        setShowAddressSuggestions(true);
      } else {
        setAddressSuggestions([]);
        setShowAddressSuggestions(false);
      }
    } catch (error) {
      setAddressSuggestions([]);
      setShowAddressSuggestions(false);
      console.error("Error searching address:", error);
    }
  };
  const [isLoading, setIsLoading] = useState(false);
  // Remove persistent error state, use toast instead
  // const [locationError, setLocationError] = useState<string | null>(null);
  // Simple toast for error display
  const showToast = (msg: string) => {
    if (typeof window !== "undefined") {
      const toast = document.createElement("div");
      toast.textContent = msg;
      toast.style.position = "fixed";
      toast.style.bottom = "32px";
      toast.style.left = "50%";
      toast.style.transform = "translateX(-50%)";
      toast.style.background = "#f87171";
      toast.style.color = "#fff";
      toast.style.padding = "12px 24px";
      toast.style.borderRadius = "8px";
      toast.style.fontSize = "15px";
      toast.style.fontWeight = "500";
      toast.style.zIndex = "9999";
      toast.style.boxShadow = "0 2px 12px rgba(0,0,0,0.12)";
      document.body.appendChild(toast);
      setTimeout(() => {
        toast.style.opacity = "0";
        setTimeout(() => document.body.removeChild(toast), 400);
      }, 2600);
    }
  };
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const updateAddress = async (coords: [number, number]) => {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${coords[0]},${coords[1]}.json?language=fr&access_token=${MAPBOX_TOKEN}`
      );
      const data = await response.json();
      if (data.features && data.features.length > 0) {
        setStreetAddress(data.features[0].place_name);
        handleInputChange({
          target: {
            name: "streetAddress",
            value: data.features[0].place_name,
          },
        } as React.ChangeEvent<HTMLInputElement>);
      }
    } catch (error) {
      console.error("Error fetching address:", error);
    }
  };

  // Unified location handler for both button and icon
  const handleGetCurrentLocation = async (
    e?: React.MouseEvent<HTMLButtonElement>
  ) => {
    if (e) e.preventDefault();
    setIsLoading(true);
    if (!navigator.geolocation) {
      showToast("Geolocation is not supported by your browser.");
      setIsLoading(false);
      return;
    }
    // Try high accuracy first
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        setCoordinates([longitude, latitude]);
        if (map && marker) {
          map.flyTo({ center: [longitude, latitude], zoom: 15 });
          // No need to move marker as it will be updated by the map's move event
        }
        // Address will be updated by the map's moveend event
        setIsLoading(false);
      },
      (error) => {
        // Retry with low accuracy if high accuracy fails
        navigator.geolocation.getCurrentPosition(
          async (position) => {
            const { latitude, longitude } = position.coords;
            setCoordinates([longitude, latitude]);
            if (map && marker) {
              map.flyTo({ center: [longitude, latitude], zoom: 15 });
              // No need to move marker as it will be updated by the map's move event
            }
            // Address will be updated by the map's moveend event
            setIsLoading(false);
          },
          (finalError) => {
            showToast(
              "Unable to retrieve your location. Please check your browser settings and try again."
            );
            setIsLoading(false);
          },
          {
            enableHighAccuracy: false,
            timeout: 10000,
            maximumAge: 0,
          }
        );
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0,
      }
    );
  };

  const searchAddress = async () => {
    if (!streetAddress) return;

    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
          streetAddress
        )}.json?country=MA&language=fr&access_token=${MAPBOX_TOKEN}`
      );
      const data = await response.json();

      if (data.features && data.features.length > 0) {
        const [lng, lat] = data.features[0].center;

        if (map) {
          // Just fly to the location - marker will be updated by the map's move event
          // and address will be updated by the map's moveend event
          map.flyTo({ center: [lng, lat], zoom: 15 });
        }
      }
    } catch (error) {
      console.error("Error searching address:", error);
    }
  };

  const handleSetLocation = async () => {
    if (!coordinates || !streetAddress.trim()) return;

    setIsLoading(true);

    try {
      // Check if user is authenticated
      const token =
        localStorage.getItem("authToken") ||
        sessionStorage.getItem("authToken");
      if (!token) {
        console.error("No authentication token found");
        toast.error("Authentication required. Please complete step 1 first.");
        handleStepChange(1);
        return;
      }

      console.log(
        "Creating office with token:",
        token.substring(0, 10) + "..."
      );

      // Get city ID from formData
      const cityId = formData.city || formData.cityId || "";
      const province = formData.province || "";

      // Format the location object as expected by the API
      const locationObj = {
        type: "Point",
        coordinates: [coordinates[0], coordinates[1]],
      };

      // Ensure phone numbers are properly formatted
      const primaryPhoneNumber = formData.phoneNumber || "";
      const whatsappNumber = formData.whatsappNumber || "";
      const otherNumber = formData.otherNumber || "";

      // Get the logo ID - handle both string and object formats
      let logoId = "";
      let logoObject = { id: "" }; // Initialize with empty string ID

      if (typeof formData.logo === "string") {
        logoId = formData.logo;
        logoObject = { id: formData.logo };
      } else if (formData.logo && typeof formData.logo === "object") {
        logoId = (formData.logo as any).id || "";
        logoObject = { id: logoId };
      }

      console.log("Logo object being sent:", logoObject);

      // Get email from formData or use default
      const email = formData.email || "<EMAIL>";

      // Prepare the office data
      const officeData = {
        officeName: formData.officeName,
        registrationNumber: formData.registrationNumber,
        currencyExchangeLicenseNumber: formData.licenseNumber,
        address: streetAddress,
        city: cityId, // Use cityId as required by the API
        state: province || formData.cityName || cityId, // Use cityName as state if province is not provided
        primaryPhoneNumber: primaryPhoneNumber,
        secondaryPhoneNumber: otherNumber || "",
        thirdPhoneNumber: "", // Not provided in the form
        whatsappNumber: whatsappNumber,
        logo: logoObject,
        // Pass logo as object with id property
        location: locationObj, // Pass location as object
        email: email, // Add email field
      };

      console.log("Office data being sent:", officeData);
      // Call the API to create the office
      const response = await createOffice(officeData);

      console.log("Office data being sent:", response);
      if (!response.success) {
        // Check if unauthorized
        if (response.statusCode === 401) {
          console.error("Unauthorized response:", response);
          toast.error("Your session has expired. Please log in again.");
          // Clear storage and redirect to step 1
          localStorage.removeItem("authToken");
          sessionStorage.removeItem("authToken");
          handleStepChange(1);
          return;
        }

        console.error("Office creation error response:", response);
        toast.error(
          response.message || "Failed to create office. Please try again."
        );
        setIsLoading(false);
        return;
      }

      console.log("Office creation success response:", response.data);
      toast.success("Office created successfully!");
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error creating office:", error);
      toast.error("An unexpected error occurred. Please try again.");
      setIsLoading(false);
    }
  };

  const handleSuccessModalClose = () => {
    setShowSuccessModal(false);
    // Navigate to dashboard after successful registration
    router.push("/dashboard");
  };

  return (
    <div className="overflow-hidden w-full flex items-start md:flex-row flex-col justify-between max-w-[1440px] gap-8 mx-auto md:px-10 lg:px-20">
      <div className="flex-shrink-0 mt-2">
        <button
          onClick={() => handleStepChange(2)}
          className="flex items-center gap-1.5 text-[#111111] text-[18px] leading-[25px] font-normal cursor-pointer"
        >
          <Image
            src="/assets/arrow-back.svg"
            alt="arrow-back"
            width={24}
            height={24}
            className="rotate-180"
          />
          Back
        </button>
      </div>

      <div className="w-full max-w-[818px] mx-auto text-center">
        <h3 className="text-[#111111] font-bold text-[24px] md:text-[32px] leading-[29px] md:leading-[38px] mb-2.5">
          Set Office Location
        </h3>
        <p className="text-[#585858] text-[14px] md:text-base leading-[20px] md:leading-[22px] mb-6 md:px-2">
          Provide your office's location details to complete your profile.
        </p>

        <div className="my-[26px]">
          <div className="mb-[34px] relative w-full">
            <div
              ref={mapContainer}
              className="rounded-2xl overflow-hidden w-full"
              style={{ width: "100%", height: "400px" }}
            />
            {/* Crosshair overlay */}
            <div
              className="absolute pointer-events-none"
              style={{
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                width: "30px",
                height: "30px",
              }}
            >
              <div
                className="absolute bg-white"
                style={{
                  top: "50%",
                  left: 0,
                  transform: "translateY(-50%)",
                  height: "1px",
                  width: "100%",
                  opacity: 0.6,
                }}
              ></div>
              <div
                className="absolute bg-white"
                style={{
                  top: 0,
                  left: "50%",
                  transform: "translateX(-50%)",
                  width: "1px",
                  height: "100%",
                  opacity: 0.6,
                }}
              ></div>
            </div>
            <button
              type="button"
              onClick={handleGetCurrentLocation}
              className="absolute cursor-pointer text-nowrap w-[200px] bottom-5 left-1/2 transform -translate-x-1/2 bg-white backdrop-blur-sm px-2.5 h-[40px] rounded-md text-[#111111] text-[14px] leading-[20px] font-normal flex items-center gap-1 shadow-md hover:shadow-lg transition-shadow duration-200"
            >
              <Image src="/assets/gps.svg" alt="gps" width={16} height={16} />{" "}
              Use my current location
            </button>
            <p className="text-[#000000] text-[12px] sm:text-[14px] leading-[20px] font-normal absolute top-3 sm:top-5 left-3 text-left sm:left-5 bg-white/80 p-1 rounded">
              *Move the map to position the marker at your office location
            </p>
          </div>

          {/* Street Address Input with Moroccan city autocomplete */}
          <div className="w-full relative bg-white border border-[#DEDEDE] rounded-lg py-[18px] px-4 max-w-[398px] mx-auto h-[56px] mb-4">
            <label className="absolute -top-2 left-2.5 bg-white px-1.5 text-xs leading-[17px] font-medium text-[#111111]">
              Street Address
            </label>
            <div className="flex items-center">
              <div className="mr-1">
                <Map />
              </div>
              <input
                type="text"
                value={streetAddress}
                onChange={(e) => {
                  setStreetAddress(e.target.value);
                  handleInputChange({
                    target: { name: "streetAddress", value: e.target.value },
                  } as React.ChangeEvent<HTMLInputElement>);
                }}
                onFocus={() => {
                  setShowCitySuggestions(true);
                  setShowAddressSuggestions(true);
                }}
                onBlur={() =>
                  setTimeout(() => {
                    setShowAddressSuggestions(false);
                    setShowCitySuggestions(false);
                  }, 150)
                }
                onKeyDown={(e) => {
                  if (e.key === "Enter") fetchMapboxSuggestions(streetAddress);
                }}
                placeholder="Type your office address (start typing city name)"
                className="flex-1 bg-transparent border-0 outline-none text-[#111111] placeholder-[#585858] text-sm leading-[20px] font-normal"
                autoComplete="off"
                readOnly={false}
              />
              <button
                onClick={handleGetCurrentLocation}
                className="focus:outline-none hover:opacity-80 transition-opacity duration-200 cursor-pointer"
                title="Use my current location"
              >
                <GPS />
              </button>
            </div>
            {/* Unified dropdown: Moroccan cities and Mapbox suggestions */}
            {(showCitySuggestions || showAddressSuggestions) &&
              (citySuggestions.length > 0 || addressSuggestions.length > 0) && (
                <div className="absolute left-0 right-0 top-[56px] bg-white border border-[#DEDEDE] rounded-b-lg shadow-lg z-10 max-h-64 overflow-y-auto">
                  {citySuggestions.length > 0 && (
                    <>
                      {citySuggestions.map((city) => (
                        <div
                          key={city}
                          className="px-4 py-2 cursor-pointer hover:bg-[#f3f3f3] text-left"
                          onMouseDown={async (e) => {
                            e.preventDefault();
                            suppressSuggestionsRef.current = true;
                            if (suppressTimeoutRef.current)
                              clearTimeout(suppressTimeoutRef.current);
                            suppressTimeoutRef.current = setTimeout(() => {
                              suppressSuggestionsRef.current = false;
                            }, 800);
                            setShowCitySuggestions(false);
                            setShowAddressSuggestions(false);
                            setTimeout(async () => {
                              setStreetAddress(city);
                              handleInputChange({
                                target: { name: "streetAddress", value: city },
                              } as React.ChangeEvent<HTMLInputElement>);
                              // Update map to city location
                              try {
                                const response = await fetch(
                                  `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
                                    city + ", Morocco"
                                  )}.json?language=fr&access_token=${MAPBOX_TOKEN}`
                                );
                                const data = await response.json();
                                if (data.features && data.features.length > 0) {
                                  const [lng, lat] = data.features[0].center;
                                  if (map) {
                                    // Just fly to the location - marker will be updated by the map's move event
                                    map.flyTo({ center: [lng, lat], zoom: 13 });
                                  }
                                }
                              } catch (err) {
                                // fail silently
                              }
                            }, 0);
                          }}
                        >
                          {city}
                        </div>
                      ))}
                    </>
                  )}
                  {addressSuggestions.length > 0 && (
                    <>
                      {addressSuggestions.map((suggestion, idx) => (
                        <div
                          key={idx}
                          className="px-4 py-2 cursor-pointer hover:bg-[#f3f3f3] text-left"
                          onMouseDown={async (e) => {
                            e.preventDefault();
                            suppressSuggestionsRef.current = true;
                            if (suppressTimeoutRef.current)
                              clearTimeout(suppressTimeoutRef.current);
                            suppressTimeoutRef.current = setTimeout(() => {
                              suppressSuggestionsRef.current = false;
                            }, 800);
                            setShowCitySuggestions(false);
                            setShowAddressSuggestions(false);
                            setTimeout(async () => {
                              setStreetAddress(suggestion);
                              handleInputChange({
                                target: {
                                  name: "streetAddress",
                                  value: suggestion,
                                },
                              } as React.ChangeEvent<HTMLInputElement>);
                              // Geocode to get coordinates
                              try {
                                const response = await fetch(
                                  `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(
                                    suggestion
                                  )}.json?country=MA&language=fr&access_token=${MAPBOX_TOKEN}`
                                );
                                const data = await response.json();
                                if (data.features && data.features.length > 0) {
                                  const [lng, lat] = data.features[0].center;
                                  if (map) {
                                    // Just fly to the location - marker will be updated by the map's move event
                                    map.flyTo({ center: [lng, lat], zoom: 15 });
                                  }
                                }
                              } catch (err) {
                                // fail silently
                              }
                            }, 0);
                          }}
                        >
                          {suggestion}
                        </div>
                      ))}
                    </>
                  )}
                </div>
              )}
          </div>

          <button
            onClick={handleSetLocation}
            disabled={!coordinates || !streetAddress.trim() || isLoading}
            className={`mt-[26px] w-full h-[46px] max-w-[398px] disabled:cursor-not-allowed mx-auto cursor-pointer rounded-md relative text-[#20523C] text-base font-semibold leading-[22px] transition-all duration-200 ${
              !coordinates || !streetAddress.trim() || isLoading
                ? "bg-gray-200 opacity-50"
                : "hover:opacity-90"
            }`}
            style={
              coordinates && streetAddress.trim() && !isLoading
                ? {
                    background:
                      "radial-gradient(65.83% 94.77% at 50.34% 116.3%, #C3F63C 0%, #54D10E 100%)",
                    border: "1px solid rgba(255, 255, 255, 0.4)",
                    boxShadow:
                      "0px 4px 4px 0px #FFFFFF52 inset, 0px -4px 4px 0px #FFFFFF52 inset",
                  }
                : undefined
            }
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="w-5 h-5 border-2 border-[#20523C] border-t-transparent rounded-full animate-spin mr-2"></div>
                <span>Creating Office...</span>
              </div>
            ) : (
              "Set this Location"
            )}
          </button>
        </div>

        {isLoading && <Loader isLoading={true} />}

        {/* Success Modal */}
        {showSuccessModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-5">
            <div className="bg-white rounded-lg p-8 max-w-md w-full text-center">
              <div className="flex justify-center mb-4">
                <Image
                  src="/assets/congratulation.png"
                  alt="Success"
                  width={200}
                  height={200}
                />
              </div>
              <h3 className="text-[#111111] font-bold text-[24px] leading-[29px] mb-2">
                Congratulations!
              </h3>
              <p className="text-[#585858] text-[16px] leading-[22px] mb-6">
                Your exchange office has been successfully registered. You can
                now access your dashboard.
              </p>
              <GradientButton
                className="w-full"
                onClick={handleSuccessModalClose}
              >
                Go to Dashboard
              </GradientButton>
            </div>
          </div>
        )}
      </div>

      {/* Custom CSS to hide Mapbox logo and attribution */}
      <style jsx global>{`
        .mapboxgl-ctrl-logo {
          display: none !important;
        }
        .mapboxgl-ctrl-attrib {
          display: none !important;
        }
        .mapboxgl-ctrl-attrib-inner {
          display: none !important;
        }
      `}</style>
    </div>
  );
};

export default Step3Form;
